/*
 * Copyright (c) 2020 New Vector Ltd
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {

    compileSdk versions.compileSdk

    defaultConfig {
        minSdk versions.minSdk
        targetSdk versions.targetSdk
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility versions.sourceCompat
        targetCompatibility versions.targetCompat
    }
    kotlinOptions {
        jvmTarget = "11"
    }

    buildFeatures {
        viewBinding true
    }
}

dependencies {
    implementation project(":library:ui-styles")
    implementation project(":library:core-utils")

    implementation 'com.github.chrisbanes:PhotoView:2.3.0'

    implementation libs.androidx.core
    implementation libs.androidx.appCompat
    implementation libs.androidx.recyclerview

    implementation libs.google.material
}
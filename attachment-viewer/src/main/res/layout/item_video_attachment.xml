<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/videoThumbnailImage"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:importantForAccessibility="no"
        android:scaleType="centerInside" />

    <VideoView
        android:id="@+id/videoView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true" />

    <ImageView
        android:id="@+id/videoControlIcon"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_centerInParent="true"
        android:contentDescription="@string/a11y_play_pause"
        android:visibility="gone"
        tools:visibility="visible" />

    <ProgressBar
        android:id="@+id/videoLoaderProgress"
        style="?android:attr/progressBarStyle"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_centerInParent="true"
        android:visibility="invisible"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/videoMediaViewerErrorView"
        style="@style/Widget.Vector.TextView.Subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_margin="16dp"
        android:textColor="?colorError"
        android:visibility="gone"
        tools:text="Error"
        tools:visibility="visible" />

</RelativeLayout>

<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootContainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".AttachmentViewerActivity">

    <View
        android:id="@+id/backgroundView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:alpha="1"
        android:background="@android:color/black" />

    <FrameLayout
        android:id="@+id/dismissContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/transitionImageContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:ignore="UselessParent"
            tools:visibility="invisible">

            <ImageView
                android:id="@+id/transitionImageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                tools:ignore="ContentDescription" />

        </FrameLayout>

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/attachmentPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/transparent"
            android:orientation="horizontal"
            android:visibility="visible" />

    </FrameLayout>

</FrameLayout>
apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'kotlin-parcelize'
apply plugin: 'realm-android'

repositories {
    google()
    mavenCentral()
    jcenter()
}


buildscript {
    repositories {
        mavenCentral()
        google()
    }
    dependencies {
        classpath "io.realm:realm-gradle-plugin:10.9.0"
    }
}

android {
    testOptions.unitTests.includeAndroidResources = true

    compileSdk versions.compileSdk

    defaultConfig {
        minSdk versions.minSdk
        targetSdk versions.targetSdk

        // Multidex is useful for tests
        multiDexEnabled true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // The following argument makes the Android Test Orchestrator run its
        // "pm clear" command after each test invocation. This command ensures
        // that the app's state is completely cleared between tests.
        testInstrumentationRunnerArguments clearPackageData: 'true'

        buildConfigField "String", "SDK_VERSION", "\"1.3.19\""

        buildConfigField "String", "GIT_SDK_REVISION", "\"${gitRevision()}\""
        resValue "string", "git_sdk_revision", "\"${gitRevision()}\""
        resValue "string", "git_sdk_revision_unix_date", "\"${gitRevisionUnixDate()}\""
        resValue "string", "git_sdk_revision_date", "\"${gitRevisionDate()}\""

        // Indicates whether or not threading support is enabled
        buildConfigField "Boolean", "THREADING_ENABLED", "${isThreadingEnabled}"
        defaultConfig {
            consumerProguardFiles 'proguard-rules.pro'
        }
    }

    testOptions {
        // Comment to run on Android 12
//        execution 'ANDROIDX_TEST_ORCHESTRATOR'
    }

    buildTypes {
        debug {
            // Set to true to log privacy or sensible data, such as token
            buildConfigField "boolean", "LOG_PRIVATE_DATA", project.property("vector.debugPrivateData")
            // Set to BODY instead of NONE to enable logging
            buildConfigField "okhttp3.logging.HttpLoggingInterceptor.Level", "OKHTTP_LOGGING_LEVEL", "okhttp3.logging.HttpLoggingInterceptor.Level." + project.property("vector.httpLogLevel")
        }

        release {
            buildConfigField "boolean", "LOG_PRIVATE_DATA", "false"
            buildConfigField "okhttp3.logging.HttpLoggingInterceptor.Level", "OKHTTP_LOGGING_LEVEL", "okhttp3.logging.HttpLoggingInterceptor.Level.BASIC"
        }
    }

    adbOptions {
        installOptions "-g"
//        timeOutInMs 350 * 1000
    }

    compileOptions {
        sourceCompatibility versions.sourceCompat
        targetCompatibility versions.targetCompat
    }

    kotlinOptions {
        jvmTarget = "11"
    }

    sourceSets {
        androidTest {
            java.srcDirs += "src/sharedTest/java"
        }
        test {
            java.srcDirs += "src/sharedTest/java"
        }
    }
}

static def gitRevision() {
    def cmd = "git rev-parse --short=8 HEAD"
    return cmd.execute().text.trim()
}

static def gitRevisionUnixDate() {
    def cmd = "git show -s --format=%ct HEAD^{commit}"
    return cmd.execute().text.trim()
}

static def gitRevisionDate() {
    def cmd = "git show -s --format=%ci HEAD^{commit}"
    return cmd.execute().text.trim()
}

dependencies {
    implementation libs.jetbrains.coroutinesCore
    implementation libs.jetbrains.coroutinesAndroid

    implementation libs.androidx.appCompat
    implementation libs.androidx.core

    // Lifecycle
    implementation libs.androidx.lifecycleCommon
    implementation libs.androidx.lifecycleProcess

    // Network
    implementation libs.squareup.retrofit
    implementation libs.squareup.retrofitMoshi

    // When version of okhttp is updated (current is 4.9.3), consider removing the workaround
    // to force usage of Protocol.HTTP_1_1. Check the status of:
    // - https://github.com/square/okhttp/issues/3278
    // - https://github.com/square/okhttp/issues/4455
    // - https://github.com/square/okhttp/issues/3146
    implementation(platform("com.squareup.okhttp3:okhttp-bom:4.9.3"))
    implementation 'com.squareup.okhttp3:okhttp'
    implementation 'com.squareup.okhttp3:logging-interceptor'
    implementation 'com.squareup.okhttp3:okhttp-urlconnection'

    implementation libs.squareup.moshi
    kapt libs.squareup.moshiKotlin

    implementation libs.markwon.core

    // Image
    implementation libs.androidx.exifinterface

    // Database
    implementation 'com.github.Zhuinden:realm-monarchy:0.7.1'

    kapt 'dk.ilios:realmfieldnameshelper:2.0.0'

    // Shared Preferences
    implementation libs.androidx.preferenceKtx

    // Work
    implementation libs.androidx.work

    // FP
    implementation libs.arrow.core
    implementation libs.arrow.instances

    // olm lib is now hosted in MavenCentral
    implementation 'org.matrix.android:olm-sdk:3.2.10'

    // DI
    implementation libs.dagger.dagger
    kapt libs.dagger.daggerCompiler

    // Logging
    implementation libs.jakewharton.timber
    implementation 'com.facebook.stetho:stetho-okhttp3:1.6.0'

    // Video compression
    implementation 'com.otaliastudios:transcoder:0.10.4'

    // Exif data handling
    implementation libs.apache.commonsImaging

    // Phone number https://github.com/google/libphonenumber
    implementation 'com.googlecode.libphonenumber:libphonenumber:8.12.42'

    testImplementation libs.tests.junit
    testImplementation 'org.robolectric:robolectric:4.7.3'
    //testImplementation 'org.robolectric:shadows-support-v4:3.0'
    // Note: version sticks to 1.9.2 due to https://github.com/mockk/mockk/issues/281
    testImplementation libs.mockk.mockk
    testImplementation libs.tests.kluent
    implementation libs.jetbrains.coroutinesAndroid
    // Plant Timber tree for test
    testImplementation 'net.lachlanmckee:timber-junit-rule:1.0.1'
    // Transitively required for mocking realm as monarchy doesn't expose Rx
    testImplementation libs.rx.rxKotlin

    kaptAndroidTest libs.dagger.daggerCompiler
    androidTestImplementation libs.androidx.testCore
    androidTestImplementation libs.androidx.testRunner
    androidTestImplementation libs.androidx.testRules
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espressoCore
    androidTestImplementation libs.tests.kluent
    androidTestImplementation libs.mockk.mockkAndroid
    androidTestImplementation libs.androidx.coreTesting
    androidTestImplementation libs.jetbrains.coroutinesAndroid
    // Plant Timber tree for test
    androidTestImplementation libs.tests.timberJunitRule

    androidTestUtil libs.androidx.orchestrator
}
